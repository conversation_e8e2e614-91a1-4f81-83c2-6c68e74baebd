"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUserAuth } from "@/hooks/use-local-storage";
import {
  Download,
  Trash2,
  FileText,
  AlertCircle,
  Search,
  SortAsc,
  SortDesc,
  Eye,
  Calendar,
  User,
  FileIcon,
  ZoomIn,
  ZoomOut,
  RotateCcw,
} from "lucide-react";
import { toast } from "sonner";

// Zoom levels for preview
const ZOOM_LEVELS = [0.5, 0.75, 1, 1.25, 1.5, 2];
const DEFAULT_ZOOM_INDEX = 2; // 100%

interface SavedFile {
  id: number;
  document_data: Buffer;
  document_metadata?: string;
  user_id: number;
  created_at: string;
}

interface SavedFileMetadata {
  document_name: string;
  applicant_name: string;
  template_id?: number;
  template_name: string;
  file_status: string;
  generation_timestamp: string;
  form_data?: any;
  document_info?: any;
}

type SortOption =
  | "date_desc"
  | "date_asc"
  | "name_asc"
  | "name_desc"
  | "applicant_asc"
  | "applicant_desc"
  | "template_asc"
  | "template_desc";

export default function SavedFilesPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useUserAuth();
  const [savedFiles, setSavedFiles] = useState<SavedFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState("");
  const [sortOption, setSortOption] = useState<SortOption>("date_desc");

  // Preview modal state
  const [previewFile, setPreviewFile] = useState<SavedFile | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [previewLoading, setPreviewLoading] = useState(false);

  // Zoom state
  const [zoomIndex, setZoomIndex] = useState(DEFAULT_ZOOM_INDEX);
  const [previewDimensions, setPreviewDimensions] = useState({
    width: 0,
    height: 0,
  });

  // Fetch saved files
  const fetchSavedFiles = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/saved-files?userId=${user.id}`);
      const data = await response.json();

      if (response.ok) {
        setSavedFiles(data.savedFiles);
      } else {
        setError(data.error || "Failed to fetch saved files");
      }
    } catch (err) {
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Download a saved file
  const downloadFile = async (fileId: number) => {
    try {
      const response = await fetch(`/api/saved-files/${fileId}`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `saved-file-${fileId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success("File downloaded successfully");
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to download file");
      }
    } catch (err) {
      toast.error("Network error. Please try again.");
    }
  };

  // Delete a saved file
  const deleteFile = async (fileId: number) => {
    if (!confirm("Are you sure you want to delete this file?")) return;

    try {
      const response = await fetch(`/api/saved-files/${fileId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setSavedFiles((files) => files.filter((file) => file.id !== fileId));
        toast.success("File deleted successfully");
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete file");
      }
    } catch (err) {
      toast.error("Network error. Please try again.");
    }
  };

  // Convert PDF blob to image using PDF.js (similar to admin documents approach)
  const convertPdfToImage = async (
    pdfBlob: Blob
  ): Promise<{ imageUrl: string; width: number; height: number }> => {
    try {
      if (typeof window === "undefined") {
        throw new Error("Canvas not available on server side");
      }

      // Dynamically import PDF.js only on client side
      const pdfjsLib = await import("pdfjs-dist");

      // Configure PDF.js worker
      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

      const arrayBuffer = await pdfBlob.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

      // Get the first page
      const page = await pdf.getPage(1);
      const viewport = page.getViewport({ scale: 2 }); // High resolution like admin page

      // Create canvas (similar to admin documents approach)
      const canvas = window.document.createElement("canvas");
      const context = canvas.getContext("2d");
      if (!context) {
        throw new Error("Could not get canvas context");
      }

      canvas.width = viewport.width;
      canvas.height = viewport.height;

      // White background (matching admin documents approach)
      context.fillStyle = "#ffffff";
      context.fillRect(0, 0, canvas.width, canvas.height);

      // Render PDF page to canvas
      await page.render({
        canvasContext: context,
        viewport: viewport,
      }).promise;

      // Convert canvas to blob URL (matching admin documents approach)
      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) {
            const imageUrl = URL.createObjectURL(blob);
            resolve({
              imageUrl,
              width: viewport.width,
              height: viewport.height,
            });
          } else {
            reject(new Error("Failed to convert canvas to blob"));
          }
        }, "image/png");
      });
    } catch (error) {
      console.error("Failed to convert PDF to image:", error);
      throw error;
    }
  };

  // Preview a saved file
  const handlePreviewFile = async (file: SavedFile) => {
    setPreviewFile(file);
    setPreviewLoading(true);
    setPreviewUrl("");
    setZoomIndex(DEFAULT_ZOOM_INDEX); // Reset zoom

    try {
      const response = await fetch(`/api/saved-files/${file.id}`);
      if (response.ok) {
        const blob = await response.blob();

        // Convert PDF to image
        const { imageUrl, width, height } = await convertPdfToImage(blob);
        setPreviewUrl(imageUrl);
        setPreviewDimensions({ width, height });
      } else {
        toast.error("Failed to load document preview");
      }
    } catch (err) {
      console.error("Error converting PDF to image:", err);
      toast.error("Failed to load document preview");
    } finally {
      setPreviewLoading(false);
    }
  };

  // Zoom control functions
  const zoomIn = () => {
    if (zoomIndex < ZOOM_LEVELS.length - 1) {
      setZoomIndex(zoomIndex + 1);
    }
  };

  const zoomOut = () => {
    if (zoomIndex > 0) {
      setZoomIndex(zoomIndex - 1);
    }
  };

  const resetZoom = () => {
    setZoomIndex(DEFAULT_ZOOM_INDEX);
  };

  const getCurrentZoom = () => ZOOM_LEVELS[zoomIndex];
  const getCurrentZoomPercentage = () => Math.round(getCurrentZoom() * 100);

  // Close preview modal
  const closePreview = () => {
    setPreviewFile(null);
    setZoomIndex(DEFAULT_ZOOM_INDEX);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl("");
    }
  };

  // Filter and sort files
  const getFilteredAndSortedFiles = () => {
    let filtered = savedFiles;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = savedFiles.filter((file) => {
        let metadata: SavedFileMetadata | null = null;
        if (file.document_metadata) {
          try {
            metadata = JSON.parse(file.document_metadata);
          } catch (error) {
            console.error("Error parsing metadata for search:", error);
          }
        }

        const documentName = metadata?.document_name?.toLowerCase() || "";
        const applicantName = metadata?.applicant_name?.toLowerCase() || "";
        const templateName = metadata?.template_name?.toLowerCase() || "";

        return (
          documentName.includes(query) ||
          applicantName.includes(query) ||
          templateName.includes(query)
        );
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aMetadata: SavedFileMetadata | null = null;
      let bMetadata: SavedFileMetadata | null = null;

      if (a.document_metadata) {
        try {
          aMetadata = JSON.parse(a.document_metadata);
        } catch (error) {
          console.error("Error parsing metadata for sort:", error);
        }
      }

      if (b.document_metadata) {
        try {
          bMetadata = JSON.parse(b.document_metadata);
        } catch (error) {
          console.error("Error parsing metadata for sort:", error);
        }
      }

      switch (sortOption) {
        case "date_desc":
          return (
            new Date(
              bMetadata?.generation_timestamp || b.created_at
            ).getTime() -
            new Date(aMetadata?.generation_timestamp || a.created_at).getTime()
          );
        case "date_asc":
          return (
            new Date(
              aMetadata?.generation_timestamp || a.created_at
            ).getTime() -
            new Date(bMetadata?.generation_timestamp || b.created_at).getTime()
          );
        case "name_asc":
          return (aMetadata?.document_name || `File ${a.id}`).localeCompare(
            bMetadata?.document_name || `File ${b.id}`
          );
        case "name_desc":
          return (bMetadata?.document_name || `File ${b.id}`).localeCompare(
            aMetadata?.document_name || `File ${a.id}`
          );
        case "applicant_asc":
          return (aMetadata?.applicant_name || "").localeCompare(
            bMetadata?.applicant_name || ""
          );
        case "applicant_desc":
          return (bMetadata?.applicant_name || "").localeCompare(
            aMetadata?.applicant_name || ""
          );
        case "template_asc":
          return (aMetadata?.template_name || "").localeCompare(
            bMetadata?.template_name || ""
          );
        case "template_desc":
          return (bMetadata?.template_name || "").localeCompare(
            aMetadata?.template_name || ""
          );
        default:
          return 0;
      }
    });

    return filtered;
  };

  const filteredAndSortedFiles = getFilteredAndSortedFiles();

  useEffect(() => {
    if (!authLoading && isAuthenticated && user) {
      fetchSavedFiles();
    }
  }, [authLoading, isAuthenticated, user]);

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="space-y-6">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Show error if user is not authenticated or not a regular user
  if (!isAuthenticated || user?.role !== "regular") {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Saved Files</h1>
          <p className="text-muted-foreground">
            Access denied. This page is only available to regular users.
          </p>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You need to be logged in as a regular user to access saved files.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Saved Files
              </CardTitle>
              <CardDescription>
                View and manage your automatically saved generated documents.
                Showing {filteredAndSortedFiles.length} of {savedFiles.length}{" "}
                file{savedFiles.length !== 1 ? "s" : ""}.
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Sort Controls */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by document name, applicant, or template..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select
              value={sortOption}
              onValueChange={(value: SortOption) => setSortOption(value)}
            >
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Sort by..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date_desc">
                  <div className="flex items-center gap-2">
                    <SortDesc className="h-4 w-4" />
                    Newest First
                  </div>
                </SelectItem>
                <SelectItem value="date_asc">
                  <div className="flex items-center gap-2">
                    <SortAsc className="h-4 w-4" />
                    Oldest First
                  </div>
                </SelectItem>
                <SelectItem value="name_asc">
                  <div className="flex items-center gap-2">
                    <SortAsc className="h-4 w-4" />
                    Name A-Z
                  </div>
                </SelectItem>
                <SelectItem value="name_desc">
                  <div className="flex items-center gap-2">
                    <SortDesc className="h-4 w-4" />
                    Name Z-A
                  </div>
                </SelectItem>
                <SelectItem value="applicant_asc">
                  <div className="flex items-center gap-2">
                    <SortAsc className="h-4 w-4" />
                    Applicant A-Z
                  </div>
                </SelectItem>
                <SelectItem value="applicant_desc">
                  <div className="flex items-center gap-2">
                    <SortDesc className="h-4 w-4" />
                    Applicant Z-A
                  </div>
                </SelectItem>
                <SelectItem value="template_asc">
                  <div className="flex items-center gap-2">
                    <SortAsc className="h-4 w-4" />
                    Template A-Z
                  </div>
                </SelectItem>
                <SelectItem value="template_desc">
                  <div className="flex items-center gap-2">
                    <SortDesc className="h-4 w-4" />
                    Template Z-A
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {loading ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Card key={i}>
                  <CardHeader>
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-full" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-1/2" />
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredAndSortedFiles.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {savedFiles.length === 0
                  ? "No saved files"
                  : "No files match your search"}
              </h3>
              <p className="text-muted-foreground">
                {savedFiles.length === 0
                  ? "You haven't generated any documents yet. When you generate documents using templates, they will be automatically saved here for easy access and reuse."
                  : "Try adjusting your search terms or sort options to find the files you're looking for."}
              </p>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredAndSortedFiles.map((file) => {
                // Parse metadata
                let metadata: SavedFileMetadata | null = null;
                if (file.document_metadata) {
                  try {
                    metadata = JSON.parse(file.document_metadata);
                  } catch (error) {
                    console.error("Error parsing metadata:", error);
                  }
                }

                return (
                  <Card
                    key={file.id}
                    className="hover:bg-muted/50 transition-colors cursor-pointer"
                    onClick={() => handlePreviewFile(file)}
                    title={`Click to preview ${
                      metadata?.document_name || `File #${file.id}`
                    }`}
                  >
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base">
                        <FileIcon className="h-4 w-4" />
                        {metadata?.document_name || `Saved File #${file.id}`}
                      </CardTitle>
                      <CardDescription className="space-y-1">
                        {metadata?.applicant_name && (
                          <div className="flex items-center gap-1 text-xs">
                            <User className="h-3 w-3" />
                            {metadata.applicant_name}
                          </div>
                        )}
                        {metadata?.template_name && (
                          <Badge variant="secondary" className="text-xs">
                            {metadata.template_name}
                          </Badge>
                        )}
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          {new Date(
                            metadata?.generation_timestamp || file.created_at
                          ).toLocaleDateString()}
                        </div>
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePreviewFile(file);
                          }}
                          className="flex items-center gap-2"
                        >
                          <Eye className="h-4 w-4" />
                          Preview
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            downloadFile(file.id);
                          }}
                          className="flex items-center gap-2"
                        >
                          <Download className="h-4 w-4" />
                          Download
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteFile(file.id);
                          }}
                          className="flex items-center gap-2 text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Preview Modal */}
      <Dialog
        open={!!previewFile}
        onOpenChange={(open) => !open && closePreview()}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {previewFile &&
                (() => {
                  let metadata: SavedFileMetadata | null = null;
                  if (previewFile.document_metadata) {
                    try {
                      metadata = JSON.parse(previewFile.document_metadata);
                    } catch (error) {
                      console.error("Error parsing metadata:", error);
                    }
                  }
                  return (
                    metadata?.document_name || `Saved File #${previewFile.id}`
                  );
                })()}
            </DialogTitle>
            <DialogDescription>
              {previewFile &&
                (() => {
                  let metadata: SavedFileMetadata | null = null;
                  if (previewFile.document_metadata) {
                    try {
                      metadata = JSON.parse(previewFile.document_metadata);
                    } catch (error) {
                      console.error("Error parsing metadata:", error);
                    }
                  }
                  return (
                    <div className="flex flex-wrap gap-4 text-sm">
                      {metadata?.applicant_name && (
                        <span>Applicant: {metadata.applicant_name}</span>
                      )}
                      {metadata?.template_name && (
                        <span>Template: {metadata.template_name}</span>
                      )}
                      <span>
                        Generated:{" "}
                        {new Date(
                          metadata?.generation_timestamp ||
                            previewFile.created_at
                        ).toLocaleDateString()}
                      </span>
                    </div>
                  );
                })()}
            </DialogDescription>
          </DialogHeader>

          {/* Zoom Controls */}
          {previewUrl && !previewLoading && (
            <div className="flex items-center justify-between border-b pb-4 mb-4">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={zoomOut}
                  disabled={zoomIndex === 0}
                  className="flex items-center gap-1"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium min-w-[60px] text-center">
                  {getCurrentZoomPercentage()}%
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={zoomIn}
                  disabled={zoomIndex === ZOOM_LEVELS.length - 1}
                  className="flex items-center gap-1"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetZoom}
                  className="flex items-center gap-1 ml-2"
                >
                  <RotateCcw className="h-4 w-4" />
                  Reset
                </Button>
              </div>
            </div>
          )}

          <div className="flex-1 overflow-auto">
            {previewLoading ? (
              <div className="space-y-4">
                <div className="h-[600px] w-full border rounded-lg bg-muted/10 flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">
                      Converting PDF to preview...
                    </p>
                  </div>
                </div>
              </div>
            ) : previewUrl ? (
              <div className="w-full">
                {getCurrentZoom() === 1 ? (
                  <img
                    src={previewUrl}
                    alt="Document Preview"
                    className="max-w-full h-auto border rounded-lg"
                  />
                ) : (
                  <div className="overflow-auto max-h-[60vh] border rounded-lg">
                    <img
                      src={previewUrl}
                      alt="Document Preview"
                      className="block"
                      style={{
                        width: `${
                          (previewDimensions.width * getCurrentZoom()) / 2
                        }px`,
                        height: `${
                          (previewDimensions.height * getCurrentZoom()) / 2
                        }px`,
                        maxWidth: "none",
                      }}
                    />
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div className="h-[600px] w-full border rounded-lg bg-muted/10 flex items-center justify-center">
                  <div className="text-center">
                    <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      Failed to load document preview
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => previewFile && downloadFile(previewFile.id)}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>
            <Button variant="outline" onClick={closePreview}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
