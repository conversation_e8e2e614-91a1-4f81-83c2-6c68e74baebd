import sqlite3 from 'sqlite3';
import path from 'path';
import fs from 'fs';

// Enable verbose mode for debugging (can be disabled in production)
const sqlite = sqlite3.verbose();

// Database file path - configurable via environment variable or default to /data/ldis.db
const DB_PATH = process.env.DATABASE_PATH || path.join(process.cwd(), 'data', 'ldis.db');

// Ensure the database directory exists
const dbDir = path.dirname(DB_PATH);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Database instance
let db: sqlite3.Database | null = null;

/**
 * Initialize the SQLite database connection
 * Creates the database file if it doesn't exist and sets up tables
 */
export function initializeDatabase(): Promise<sqlite3.Database> {
  return new Promise((resolve, reject) => {
    if (db) {
      // If database is already initialized, still ensure tables exist
      createTables()
        .then(() => resolve(db!))
        .catch(reject);
      return;
    }

    db = new sqlite.Database(DB_PATH, (err) => {
      if (err) {
        console.error('Error opening database:', err.message);
        reject(err);
        return;
      }

      console.log('Connected to SQLite database at:', DB_PATH);

      // Create tables if they don't exist
      createTables()
        .then(() => resolve(db!))
        .catch(reject);
    });
  });
}

/**
 * Create the users and templates tables if they don't exist
 */
function createTables(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    // Enable foreign keys
    db!.run('PRAGMA foreign_keys = ON', (err) => {
      if (err) {
        console.error('Error enabling foreign keys:', err.message);
        reject(err);
        return;
      }

      const createUsersTable = `
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          recovery_key TEXT,
          role TEXT NOT NULL DEFAULT 'admin',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;

      db!.run(createUsersTable, (err) => {
        if (err) {
          console.error('Error creating users table:', err.message);
          reject(err);
          return;
        }

        console.log('Users table created or already exists');

        // Migration: Add role column if it doesn't exist
        db!.run(`ALTER TABLE users ADD COLUMN role TEXT NOT NULL DEFAULT 'admin'`, (err) => {
          if (err && !err.message.includes('duplicate column name')) {
            console.error('Error adding role column:', err.message);
          }
        });

        // Create templates table
        const createTemplatesTable = `
          CREATE TABLE IF NOT EXISTS templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            template_name TEXT NOT NULL,
            description TEXT,
            filename TEXT NOT NULL,
            placeholders TEXT,
            layout_size TEXT,
            uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            user_id INTEGER NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
          )
        `;

        db!.run(createTemplatesTable, (err) => {
          if (err) {
            console.error('Error creating templates table:', err.message);
            reject(err);
            return;
          }

          console.log('Templates table created or already exists');

          // Create documents table
          const createDocumentsTable = `
            CREATE TABLE IF NOT EXISTS documents (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              document_name TEXT NOT NULL,
              applicant_name TEXT NOT NULL,
              uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              document_data BLOB,
              status TEXT DEFAULT 'to review',
              approved_at DATETIME,
              approved_by INTEGER,
              user_id INTEGER NOT NULL,
              code TEXT UNIQUE,
              FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
              FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
            )
          `;

          db!.run(createDocumentsTable, (err) => {
            if (err) {
              console.error('Error creating documents table:', err.message);
              reject(err);
              return;
            }

            console.log('Documents table created or already exists');

            // Create notifications table
            const createNotificationsTable = `
              CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_id INTEGER NOT NULL,
                document_name TEXT NOT NULL,
                applicant_name TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER NOT NULL,
                FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
              )
            `;

            db!.run(createNotificationsTable, (err) => {
              if (err) {
                console.error('Error creating notifications table:', err.message);
                reject(err);
                return;
              }

              console.log('Notifications table created or already exists');

              // Create archives table
              const createArchivesTable = `
                CREATE TABLE IF NOT EXISTS archives (
                  id INTEGER PRIMARY KEY AUTOINCREMENT,
                  document_name TEXT NOT NULL,
                  applicant_name TEXT NOT NULL,
                  uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                  document_data BLOB,
                  status TEXT DEFAULT 'approved',
                  user_id INTEGER NOT NULL,
                  document_id INTEGER NOT NULL,
                  approved_at DATETIME,
                  approved_by INTEGER,
                  code TEXT,
                  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                  FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
                )
              `;

              db!.run(createArchivesTable, (err) => {
                if (err) {
                  console.error('Error creating archives table:', err.message);
                  reject(err);
                  return;
                }

                console.log('Archives table created or already exists');

                // Create saved_files table
                const createSavedFilesTable = `
                  CREATE TABLE IF NOT EXISTS saved_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    document_name TEXT NOT NULL,
                    applicant_name TEXT NOT NULL,
                    template_id INTEGER,
                    template_name TEXT NOT NULL,
                    document_data BLOB NOT NULL,
                    document_metadata TEXT,
                    file_status TEXT DEFAULT 'generated',
                    generation_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    user_id INTEGER NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE SET NULL
                  )
                `;

                db!.run(createSavedFilesTable, (err) => {
                  if (err) {
                    console.error('Error creating saved_files table:', err.message);
                    reject(err);
                    return;
                  }

                  console.log('Saved files table created or already exists');

                  // Run migration for saved_files table
                  migrateSavedFilesTable().then(() => {
                    resolve();
                  }).catch((migrationErr) => {
                    console.error('Error during saved_files migration:', migrationErr.message);
                    resolve(); // Continue even if migration fails
                  });
                });
              });
            });
          });
        });
      });
    });
  });
}

/**
 * Get the database instance
 * Initializes the database if not already done
 */
export async function getDatabase(): Promise<sqlite3.Database> {
  if (!db) {
    return await initializeDatabase();
  }
  return db;
}

/**
 * Close the database connection
 */
export function closeDatabase(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!db) {
      resolve();
      return;
    }

    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err.message);
        reject(err);
        return;
      }
      
      console.log('Database connection closed');
      db = null;
      resolve();
    });
  });
}

/**
 * Execute a query with parameters
 */
export function runQuery(sql: string, params: unknown[] = []): Promise<sqlite3.RunResult> {
  return new Promise(async (resolve, reject) => {
    try {
      const database = await getDatabase();
      
      database.run(sql, params, function(err) {
        if (err) {
          console.error('Error executing query:', err.message);
          reject(err);
          return;
        }
        
        resolve(this);
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Get a single row from a query
 */
export function getRow<T = unknown>(sql: string, params: unknown[] = []): Promise<T | undefined> {
  return new Promise(async (resolve, reject) => {
    try {
      const database = await getDatabase();
      
      database.get(sql, params, (err, row) => {
        if (err) {
          console.error('Error executing query:', err.message);
          reject(err);
          return;
        }
        
        resolve(row as T);
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Get all rows from a query
 */
export function getAllRows<T = unknown>(sql: string, params: unknown[] = []): Promise<T[]> {
  return new Promise(async (resolve, reject) => {
    try {
      const database = await getDatabase();
      
      database.all(sql, params, (err, rows) => {
        if (err) {
          console.error('Error executing query:', err.message);
          reject(err);
          return;
        }
        
        resolve(rows as T[]);
      });
    } catch (error) {
      reject(error);
    }
  });
}

// User-related database operations
export interface User {
  id: number;
  username: string;
  password: string;
  recovery_key?: string;
  role: 'admin' | 'regular';
  created_at: string;
}

/**
 * Create a new user
 */
export async function createUser(username: string, hashedPassword: string, recoveryKey?: string, role: 'admin' | 'regular' = 'admin'): Promise<number> {
  const result = await runQuery(
    'INSERT INTO users (username, password, recovery_key, role) VALUES (?, ?, ?, ?)',
    [username, hashedPassword, recoveryKey, role]
  );

  return result.lastID!;
}

/**
 * Get a user by username
 */
export async function getUserByUsername(username: string): Promise<User | undefined> {
  return await getRow<User>('SELECT * FROM users WHERE username = ?', [username]);
}

/**
 * Get a user by ID
 */
export async function getUserById(id: number): Promise<User | undefined> {
  return await getRow<User>('SELECT * FROM users WHERE id = ?', [id]);
}

/**
 * Update user password
 */
export async function updateUserPassword(id: number, hashedPassword: string): Promise<void> {
  await runQuery('UPDATE users SET password = ? WHERE id = ?', [hashedPassword, id]);
}

/**
 * Update user recovery key
 */
export async function updateUserRecoveryKey(id: number, recoveryKey: string): Promise<void> {
  await runQuery('UPDATE users SET recovery_key = ? WHERE id = ?', [recoveryKey, id]);
}

/**
 * Get all users (for admin purposes)
 */
export async function getAllUsers(): Promise<User[]> {
  return await getAllRows<User>('SELECT * FROM users ORDER BY created_at DESC');
}

/**
 * Delete a user
 */
export async function deleteUser(id: number): Promise<void> {
  await runQuery('DELETE FROM users WHERE id = ?', [id]);
}

// Template-related database operations
export interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders?: string;
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
}

// Document-related database operations
export interface Document {
  id: number;
  document_name: string;
  applicant_name: string;
  uploaded_at: string;
  document_data?: Buffer;
  status: string;
  approved_at?: string;
  approved_by?: number;
  user_id: number;
  code?: string;
}

// Notification-related database operations
export interface Notification {
  id: number;
  document_id: number;
  document_name: string;
  applicant_name: string;
  is_read: boolean;
  uploaded_at: string;
  user_id: number;
}

// Saved files-related database operations
export interface SavedFile {
  id: number;
  document_name: string;
  applicant_name: string;
  template_id?: number;
  template_name: string;
  document_data: Buffer;
  document_metadata?: string;
  file_status: string;
  generation_timestamp: string;
  user_id: number;
  created_at: string;
}



// Archive-related database operations
export interface Archive {
  id: number;
  document_name: string;
  applicant_name: string;
  uploaded_at: string;
  document_data?: Buffer;
  status: string;
  user_id: number;
  document_id: number;
  approved_at?: string;
  approved_by?: number;
  code?: string;
}

/**
 * Create a new template
 */
export async function createTemplate(
  templateName: string,
  description: string | undefined,
  filename: string,
  placeholders: string | undefined,
  layoutSize: string | undefined,
  userId: number
): Promise<number> {
  const result = await runQuery(
    'INSERT INTO templates (template_name, description, filename, placeholders, layout_size, user_id) VALUES (?, ?, ?, ?, ?, ?)',
    [templateName, description, filename, placeholders, layoutSize, userId]
  );

  return result.lastID!;
}

/**
 * Get a template by ID
 */
export async function getTemplateById(id: number): Promise<Template | undefined> {
  return await getRow<Template>('SELECT * FROM templates WHERE id = ?', [id]);
}

/**
 * Get all templates for a specific user
 */
export async function getTemplatesByUserId(userId: number): Promise<Template[]> {
  return await getAllRows<Template>('SELECT * FROM templates WHERE user_id = ? ORDER BY uploaded_at DESC', [userId]);
}

/**
 * Get all templates (for admin purposes)
 */
export async function getAllTemplates(): Promise<Template[]> {
  return await getAllRows<Template>('SELECT * FROM templates ORDER BY uploaded_at DESC');
}

/**
 * Update template information
 */
export async function updateTemplate(
  id: number,
  templateName: string,
  description: string | undefined,
  placeholders: string | undefined,
  layoutSize: string | undefined
): Promise<void> {
  await runQuery(
    'UPDATE templates SET template_name = ?, description = ?, placeholders = ?, layout_size = ? WHERE id = ?',
    [templateName, description, placeholders, layoutSize, id]
  );
}

/**
 * Delete a template
 */
export async function deleteTemplate(id: number): Promise<void> {
  await runQuery('DELETE FROM templates WHERE id = ?', [id]);
}

/**
 * Get templates with user information (JOIN query)
 */
export async function getTemplatesWithUserInfo(): Promise<(Template & { username: string })[]> {
  return await getAllRows<Template & { username: string }>(
    'SELECT t.*, u.username FROM templates t JOIN users u ON t.user_id = u.id ORDER BY t.uploaded_at DESC'
  );
}

/**
 * Generate a unique document code (checks both documents and archives tables)
 */
async function generateDocumentCode(): Promise<string> {
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  let sequence = 1;
  let code: string;

  // Keep trying until we find a unique code
  do {
    const sequenceStr = String(sequence).padStart(4, '0');
    code = `DOC-${date}-${sequenceStr}`;

    // Check if this code already exists in either documents or archives table
    const existingInDocuments = await getRow<{ count: number }>('SELECT COUNT(*) as count FROM documents WHERE code = ?', [code]);
    const existingInArchives = await getRow<{ count: number }>('SELECT COUNT(*) as count FROM archives WHERE code = ?', [code]);

    if (existingInDocuments && existingInDocuments.count === 0 &&
        existingInArchives && existingInArchives.count === 0) {
      break;
    }
    sequence++;
  } while (sequence < 10000); // Safety limit

  return code;
}

/**
 * Create a new document
 */
export async function createDocument(
  documentName: string,
  applicantName: string,
  documentData: Buffer | undefined,
  status: string,
  userId: number,
  code?: string
): Promise<number> {
  const currentTimestamp = new Date().toISOString();
  const documentCode = code || await generateDocumentCode();

  const result = await runQuery(
    'INSERT INTO documents (document_name, applicant_name, document_data, status, user_id, uploaded_at, code) VALUES (?, ?, ?, ?, ?, ?, ?)',
    [documentName, applicantName, documentData, status, userId, currentTimestamp, documentCode]
  );

  return result.lastID!;
}

/**
 * Get a document by ID
 */
export async function getDocumentById(id: number): Promise<Document | undefined> {
  return await getRow<Document>('SELECT * FROM documents WHERE id = ?', [id]);
}

/**
 * Get a document by code
 */
export async function getDocumentByCode(code: string): Promise<Document | undefined> {
  return await getRow<Document>('SELECT * FROM documents WHERE code = ?', [code]);
}

/**
 * Get all documents for a specific user
 */
export async function getDocumentsByUserId(userId: number): Promise<Document[]> {
  return await getAllRows<Document>('SELECT * FROM documents WHERE user_id = ? ORDER BY uploaded_at DESC', [userId]);
}

/**
 * Get all documents
 */
export async function getAllDocuments(): Promise<Document[]> {
  return await getAllRows<Document>('SELECT * FROM documents ORDER BY uploaded_at DESC');
}

/**
 * Update document status
 */
export async function updateDocumentStatus(id: number, status: string, approvedAt?: string, approvedBy?: number): Promise<void> {
  if (approvedAt && approvedBy) {
    await runQuery('UPDATE documents SET status = ?, approved_at = ?, approved_by = ? WHERE id = ?', [status, approvedAt, approvedBy, id]);
  } else if (approvedAt) {
    await runQuery('UPDATE documents SET status = ?, approved_at = ? WHERE id = ?', [status, approvedAt, id]);
  } else {
    await runQuery('UPDATE documents SET status = ? WHERE id = ?', [status, id]);
  }
}

/**
 * Delete a document
 */
export async function deleteDocument(id: number): Promise<void> {
  await runQuery('DELETE FROM documents WHERE id = ?', [id]);
}

/**
 * Get documents with user information (JOIN query)
 */
export async function getDocumentsWithUserInfo(): Promise<(Document & { username: string })[]> {
  return await getAllRows<Document & { username: string }>(
    'SELECT d.*, u.username FROM documents d JOIN users u ON d.user_id = u.id ORDER BY d.uploaded_at DESC'
  );
}

/**
 * Create a new notification
 */
export async function createNotification(
  documentId: number,
  documentName: string,
  applicantName: string,
  userId: number
): Promise<number> {
  const currentTimestamp = new Date().toISOString();

  const result = await runQuery(
    'INSERT INTO notifications (document_id, document_name, applicant_name, user_id, uploaded_at) VALUES (?, ?, ?, ?, ?)',
    [documentId, documentName, applicantName, userId, currentTimestamp]
  );

  return result.lastID!;
}

/**
 * Get a notification by ID
 */
export async function getNotificationById(id: number): Promise<Notification | undefined> {
  return await getRow<Notification>('SELECT * FROM notifications WHERE id = ?', [id]);
}

/**
 * Get all notifications
 */
export async function getAllNotifications(): Promise<Notification[]> {
  return await getAllRows<Notification>('SELECT * FROM notifications ORDER BY uploaded_at DESC');
}

/**
 * Get notifications by user ID
 */
export async function getNotificationsByUserId(userId: number): Promise<Notification[]> {
  return await getAllRows<Notification>('SELECT * FROM notifications WHERE user_id = ? ORDER BY uploaded_at DESC', [userId]);
}

/**
 * Get unread notifications by user ID
 */
export async function getUnreadNotificationsByUserId(userId: number): Promise<Notification[]> {
  return await getAllRows<Notification>('SELECT * FROM notifications WHERE user_id = ? AND is_read = FALSE ORDER BY uploaded_at DESC', [userId]);
}

/**
 * Mark notification as read
 */
export async function markNotificationAsRead(id: number): Promise<void> {
  await runQuery('UPDATE notifications SET is_read = TRUE WHERE id = ?', [id]);
}

/**
 * Mark all notifications as read for a user
 */
export async function markAllNotificationsAsRead(userId: number): Promise<void> {
  await runQuery('UPDATE notifications SET is_read = TRUE WHERE user_id = ?', [userId]);
}

/**
 * Mark all notifications as read for a specific document
 */
export async function markDocumentNotificationsAsRead(documentId: number): Promise<void> {
  await runQuery('UPDATE notifications SET is_read = TRUE WHERE document_id = ? AND is_read = FALSE', [documentId]);
}

/**
 * Delete a notification
 */
export async function deleteNotification(id: number): Promise<void> {
  await runQuery('DELETE FROM notifications WHERE id = ?', [id]);
}

/**
 * Get notifications with document and user information (JOIN query)
 */
export async function getNotificationsWithInfo(): Promise<(Notification & { username: string })[]> {
  return await getAllRows<Notification & { username: string }>(
    'SELECT n.*, u.username FROM notifications n JOIN users u ON n.user_id = u.id ORDER BY n.uploaded_at DESC'
  );
}

/**
 * Create a new archive entry
 */
export async function createArchive(
  documentName: string,
  applicantName: string,
  documentData: Buffer | undefined,
  userId: number,
  documentId: number,
  code: string,
  approvedAt?: string,
  approvedBy?: number,
  status: string = 'approved'
): Promise<number> {
  const currentTimestamp = new Date().toISOString();

  const result = await runQuery(
    'INSERT INTO archives (document_name, applicant_name, document_data, user_id, document_id, uploaded_at, approved_at, approved_by, code, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
    [documentName, applicantName, documentData, userId, documentId, currentTimestamp, approvedAt, approvedBy, code, status]
  );

  return result.lastID!;
}

/**
 * Get an archive by ID
 */
export async function getArchiveById(id: number): Promise<Archive | undefined> {
  return await getRow<Archive>('SELECT * FROM archives WHERE id = ?', [id]);
}

/**
 * Get an archive by code
 */
export async function getArchiveByCode(code: string): Promise<Archive | undefined> {
  return await getRow<Archive>('SELECT * FROM archives WHERE code = ?', [code]);
}

/**
 * Get all archives for a specific user
 */
export async function getArchivesByUserId(userId: number): Promise<Archive[]> {
  return await getAllRows<Archive>('SELECT * FROM archives WHERE user_id = ? ORDER BY uploaded_at DESC', [userId]);
}

/**
 * Get all archives
 */
export async function getAllArchives(): Promise<Archive[]> {
  return await getAllRows<Archive>('SELECT * FROM archives ORDER BY uploaded_at DESC');
}

/**
 * Get archives by document ID
 */
export async function getArchivesByDocumentId(documentId: number): Promise<Archive[]> {
  return await getAllRows<Archive>('SELECT * FROM archives WHERE document_id = ? ORDER BY uploaded_at DESC', [documentId]);
}

/**
 * Update archive approval information
 */
export async function updateArchiveApproval(id: number, approvedAt: string, approvedBy: number): Promise<void> {
  await runQuery('UPDATE archives SET approved_at = ?, approved_by = ? WHERE id = ?', [approvedAt, approvedBy, id]);
}

/**
 * Delete an archive
 */
export async function deleteArchive(id: number): Promise<void> {
  await runQuery('DELETE FROM archives WHERE id = ?', [id]);
}

/**
 * Get archives with user information (JOIN query)
 */
export async function getArchivesWithUserInfo(): Promise<(Archive & { username: string; approved_by_username?: string })[]> {
  return await getAllRows<Archive & { username: string; approved_by_username?: string }>(
    `SELECT a.*, u.username, u2.username as approved_by_username
     FROM archives a
     JOIN users u ON a.user_id = u.id
     LEFT JOIN users u2 ON a.approved_by = u2.id
     ORDER BY a.uploaded_at DESC`
  );
}

/**
 * Get archives by applicant name
 */
export async function getArchivesByApplicantName(applicantName: string): Promise<Archive[]> {
  return await getAllRows<Archive>('SELECT * FROM archives WHERE applicant_name = ? ORDER BY uploaded_at DESC', [applicantName]);
}

/**
 * Get archives by document name
 */
export async function getArchivesByDocumentName(documentName: string): Promise<Archive[]> {
  return await getAllRows<Archive>('SELECT * FROM archives WHERE document_name = ? ORDER BY uploaded_at DESC', [documentName]);
}

/**
 * Get count of unread notifications for a user
 */
export async function getUnreadNotificationCount(userId: number): Promise<number> {
  const result = await getRow<{ count: number }>('SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = FALSE', [userId]);
  return result?.count || 0;
}

// Saved files-related database operations

/**
 * Create a new saved file
 */
export async function createSavedFile(
  documentName: string,
  applicantName: string,
  templateId: number | undefined,
  templateName: string,
  documentData: Buffer,
  documentMetadata: string | undefined,
  userId: number,
  fileStatus: string = 'generated'
): Promise<number> {
  const result = await runQuery(
    'INSERT INTO saved_files (document_name, applicant_name, template_id, template_name, document_data, document_metadata, file_status, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
    [documentName, applicantName, templateId, templateName, documentData, documentMetadata, fileStatus, userId]
  );

  return result.lastID!;
}

/**
 * Get a saved file by ID
 */
export async function getSavedFileById(id: number): Promise<SavedFile | undefined> {
  return await getRow<SavedFile>('SELECT * FROM saved_files WHERE id = ?', [id]);
}

/**
 * Get all saved files for a user
 */
export async function getSavedFilesByUserId(userId: number): Promise<SavedFile[]> {
  return await getAllRows<SavedFile>('SELECT * FROM saved_files WHERE user_id = ? ORDER BY created_at DESC', [userId]);
}

/**
 * Delete a saved file
 */
export async function deleteSavedFile(id: number): Promise<void> {
  await runQuery('DELETE FROM saved_files WHERE id = ?', [id]);
}

/**
 * Migrate saved_files table to add new columns if they don't exist
 */
export async function migrateSavedFilesTable(): Promise<void> {
  try {
    // Check if the new columns exist by trying to select them
    await runQuery('SELECT document_name, applicant_name, template_id, template_name, document_metadata, file_status, generation_timestamp FROM saved_files LIMIT 1', []);
    console.log('Saved files table already has new columns');
  } catch (error) {
    console.log('Migrating saved_files table to add new columns...');

    // Add new columns one by one
    const migrations = [
      'ALTER TABLE saved_files ADD COLUMN document_name TEXT DEFAULT "Unknown Document"',
      'ALTER TABLE saved_files ADD COLUMN applicant_name TEXT DEFAULT "Unknown Applicant"',
      'ALTER TABLE saved_files ADD COLUMN template_id INTEGER',
      'ALTER TABLE saved_files ADD COLUMN template_name TEXT DEFAULT "Unknown Template"',
      'ALTER TABLE saved_files ADD COLUMN document_metadata TEXT',
      'ALTER TABLE saved_files ADD COLUMN file_status TEXT DEFAULT "generated"',
      'ALTER TABLE saved_files ADD COLUMN generation_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP'
    ];

    for (const migration of migrations) {
      try {
        await runQuery(migration, []);
      } catch (migrationError) {
        // Column might already exist, continue with next migration
        console.log(`Migration step skipped (column might already exist): ${migration}`);
      }
    }

    console.log('Saved files table migration completed');
  }
}

/**
 * Delete all saved files for a user
 */
export async function deleteSavedFilesByUserId(userId: number): Promise<void> {
  await runQuery('DELETE FROM saved_files WHERE user_id = ?', [userId]);
}



// Initialize database on module load for Next.js
if (typeof window === 'undefined') {
  // Only initialize on server-side
  initializeDatabase().catch(console.error);
}
